package com.xhgj.srm.v2.aop;/**
 * @since 2025/4/17 19:18
 */

import com.xhgj.srm.jpa.sharding.enums.VersionEnum;
import com.xhgj.srm.jpa.sharding.util.ShardingContext;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class ThreadV2LocalModifierAspect {
  /**
   * 记录当前线程是否由此切面设置了版本信息以及设置的嵌套深度
   */
  private static final ThreadLocal<Integer> ASPECT_DEPTH = new ThreadLocal<>();

  @Before("execution(* com.xhgj.srm.v2.service..*.*(..))")
  public void modifyThreadLocalBeforeExecution() {
    Integer depth = ASPECT_DEPTH.get();
    if (ShardingContext.getVersion() == null || depth != null) {
      if (depth == null) {
        depth = 0;
      }
      log.info("ThreadV2LocalModifierAspect modifyThreadLocalBeforeExecution");
      ShardingContext.setVersion(VersionEnum.V2);
      // 嵌套深度加1
      ASPECT_DEPTH.set(depth + 1);
    }
  }

  @After("execution(* com.xhgj.srm.v2.service..*.*(..))")
  public void cleanupThreadLocal() {
    Integer depth = ASPECT_DEPTH.get();
    if (depth != null) {
      depth = depth - 1;
      // 只有最外层方法执行完才清理ThreadLocal
      if (depth == 0) {
        log.info("ThreadV2LocalModifierAspect cleanupThreadLocal");
        ShardingContext.clear();
        ASPECT_DEPTH.remove();
      } else {
        ASPECT_DEPTH.set(depth);
      }
    }
  }
}
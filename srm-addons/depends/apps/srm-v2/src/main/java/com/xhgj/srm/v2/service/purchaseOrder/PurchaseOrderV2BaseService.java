package com.xhgj.srm.v2.service.purchaseOrder;/**
 * @since 2025/4/28 9:40
 */

import com.xhgj.srm.common.dto.ApprovalResult;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderSyncStatus;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.request.dto.mdm.OrgDomain;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_086Result.ReturnMessage;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_087Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_088Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.SAPReversalDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.SapBomInventoryInquiries;
import com.xhgj.srm.request.service.third.erp.sap.dto.MdmCostCenterParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.SapMaterialCardInquiriesParam;
import com.xhgj.srm.v2.form.PurchaseOrderAddV2Form;
import com.xhgj.srm.v2.form.purchaseOrder.purchaseOrderExport;
import com.xhgj.srm.v2.dto.UpdateSupplierOrderBaseInfoV2DTO;
import com.xhgj.srm.v2.vo.PurchaseOrderV2DetailedVO;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderStatistics;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.v2.dto.purchaseOrder.SupplierOrderCountV2DTO;
import com.xhgj.srm.v2.form.purchaseOrder.PurchaseOrderTableHeaderV2Query;
import com.xhgj.srm.v2.form.purchaseOrder.PurchaseOrderV2PageQuery;
import com.xhgj.srm.v2.vo.purchaseOrder.SupplierOrderSyncVO;
import com.xhgj.srm.v2.vo.purchaseOrder.PurchaseOrderV2ListVO;
import com.xhiot.boot.mvc.base.PageResult;
import java.math.BigDecimal;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/4/28 09:40:33
 *@description
 */
public interface PurchaseOrderV2BaseService {

  /**
   * 分页查询采购订单
   */
  PageResult<PurchaseOrderV2ListVO> getPagePurchaseOrderPageRef(PurchaseOrderV2PageQuery query, User user);

  /**
   * 获得订单状态对应的数量
   * @param query
   * @param user
   * @return
   */
  SupplierOrderCountV2DTO getSupplierOrderCount(PurchaseOrderV2PageQuery query, User user);

  /**
   * 查询采购订单统计forOrder
   * @param query
   * @return
   */
  PurchaseOrderStatistics getPagePurchaseOrderStatisticsForOrder(PurchaseOrderV2PageQuery query,  User user);

  /**
   * 采购订单表头查询
   * @param param
   * @return
   */
  List<Object> getOrderListByTableHeaderRef(PurchaseOrderTableHeaderV2Query param, User user);

  /**
   * 单据数量统计
   * @param purchaseOrderId
   * @return
   */
  SupplierOrderCountV2DTO getSupplierOrderFormCountById(String purchaseOrderId);

  /**
   * 采购订单新增
   * @param form
   */
  String addOrUpdatePurchaseOrder(PurchaseOrderAddV2Form form, User user);

  /**
   * 采购订单飞搭审核回调
   */
  void audit(ApprovalResult approvalResult, SupplierOrderSyncStatus status);

  /**
   * 重新推送采购订单中的系统信息
   * @param id
   * @param syncType
   * @param user
   */
  void rePushPurchaseOrder(String id, String syncType, User user);

  /**
   * 获取采购订单系统信息中同步记录
   * @param id
   * @return
   */
  SupplierOrderSyncVO getPurchaseOrderSyncRecord(String id);

  /**
   * 采购订单详情
   * @param id
   * @return
   */
  PurchaseOrderV2DetailedVO getPurchaseOrderDetailed(String id);

  /**
   * 更新采购订单基本信息
   * @param params
   */
  void updateSupplierOrderBaseInfo(UpdateSupplierOrderBaseInfoV2DTO params);

  /**
   * 导出采购订单
   * @param export
   * @param user
   */
  void exportSupplierOrder(purchaseOrderExport export, User user);

  /**
   * 采购订单删除 - 驳回暂存下的采购订单，仅admin和订单采购员本人展示删除按钮
   * @param ids
   * @param user
   */
  void deletePurchaseOrder(List<String> ids, User user);

  List<ReturnMessage> sapOrderInquiries(SapMaterialCardInquiriesParam param);

  List<MM_087Result.ReturnMessage> sapMaterialCardInquiries(SapMaterialCardInquiriesParam param);

  /**
   * SAP BOM清单查询
   * @param param sapBomInventoryInquiries
   * @return List<MM_088Result.ReturnMessage>
   */
  List<MM_088Result.ReturnMessage> sapBomInventoryInquiries(SapBomInventoryInquiries param);

  List<OrgDomain> mdmCostCenterInquiries(MdmCostCenterParam param);

  /**
   * 设置入库进度
   * @param supplierOrder 采购订单
   */
  String setStockProgress(SupplierOrderV2 supplierOrder);

  /**
   * 获取采购订单的入库总数量
   */
  BigDecimal getInventoryQuantity(String supplierOrderId);
}

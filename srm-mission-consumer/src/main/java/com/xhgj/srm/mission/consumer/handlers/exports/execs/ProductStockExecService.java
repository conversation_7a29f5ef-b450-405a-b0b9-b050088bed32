package com.xhgj.srm.mission.consumer.handlers.exports.execs;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.common.utils.HttpUtil;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.mission.common.MissionDispatchParam;
import com.xhgj.srm.mission.consumer.framework.service.FcMissionService;
import com.xhgj.srm.mission.consumer.handlers.ExecService;
import com.xhgj.srm.mission.consumer.handlers.exports.ExportMissionCompleteResult;
import com.xhgj.srm.mission.consumer.handlers.exports.execs.domain.ProductStockDTO;
import com.xhgj.srm.mission.consumer.handlers.exports.execs.params.ExportProductInventoryMdmDTO;
import com.xhgj.srm.mission.consumer.handlers.exports.execs.params.ExportProductInventoryMdmDTO.DataDTO.ContentDTO.AreaInventoryDTO;
import com.xhgj.srm.mission.consumer.handlers.exports.execs.params.ExportProductParams;
import com.xhgj.srm.request.utils.DownloadThenUpUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.ExcelUtil;
import com.xhiot.boot.mvc.base.ResultBean;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * ProductStockExportExec
 */
@Slf4j
@Component
public class ProductStockExecService implements ExecService {

  @Autowired
  private HttpUtil httpUtil;
  @Autowired
  private ExportUtil exportUtil;
  @Autowired
  private FcMissionService fcMissionService;
  @Autowired
  private DownloadThenUpUtil downloadThenUpUtil;

  private static final String PRODUCT_STOCK_DETAIL = "srm/upload/物料库存导出模板.xlsx";

  @SneakyThrows
  @Override
  public ExportMissionCompleteResult exec(Collection<?> collection,
      MissionDispatchParam dispatchParam) {
    try( InputStream inputStream = downloadThenUpUtil.getInputStreamFromOSS(PRODUCT_STOCK_DETAIL);
        Workbook book = ExcelUtil.buildByFile("物料库存导出模板.xlsx", inputStream);
    ) {
      String platformName = StrUtil.EMPTY;
      ExportProductParams exportProductParams =
          JSON.parseObject(dispatchParam.getParams(), new TypeReference<ExportProductParams>() {});
      if (exportProductParams != null && StrUtil.isNotBlank(exportProductParams.getPlatformName())) {
        platformName = exportProductParams.getPlatformName();
      }
      Sheet sheet = book.getSheetAt(0);
      CellStyle baseStyle = exportUtil.getBaseStyle(book);
      int startRow = 1;
      // 假设从第二行开始写入数据
      int index = 1;
      int count = 0;
      List<ProductStockDTO> detailExportDTOList = (List<ProductStockDTO>) collection;
      for (ProductStockDTO productStockDTO : detailExportDTOList) {
        Row row = sheet.createRow(startRow);
        int rowNum = index + 1;
        exportUtil.createCell(row, 0, productStockDTO.getProductCode(), baseStyle);
        exportUtil.createCell(row, 1, productStockDTO.getProductName(), baseStyle);
        exportUtil.createCell(row, 2, productStockDTO.getModel(), baseStyle);
        exportUtil.createCell(row, 3, productStockDTO.getUnit(), baseStyle);
        exportUtil.createCell(row, 4, productStockDTO.getSellArea(), baseStyle);
        exportUtil.createCell(row, 5, productStockDTO.getStock(), baseStyle);
        startRow += 1;
        count += 1;
        index++;
        fcMissionService.createMissionDetail(
            dispatchParam.getMissionId(), "第【" + rowNum + "】行", StrUtil.EMPTY);
      }
      ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
      book.write(outputStream);
      return ExportMissionCompleteResult.builder()
          .successCount(count)
          .fileName(platformName + "物料区域库存" + System.currentTimeMillis() + ".xlsx")
          .bytes(outputStream.toByteArray())
          .build();
    }
  }

  @Override
  public Collection<?> prepare(MissionDispatchParam dispatchParam) {
    ExportProductParams exportProductParams =
        JSON.parseObject(dispatchParam.getParams(), new TypeReference<ExportProductParams>() {});
    if (exportProductParams == null || StrUtil.isBlank(exportProductParams.getMdmCode())
        || StrUtil.isBlank(exportProductParams.getPlatformCode())) {
      throw new CheckException("params转换异常");
    }
    //仅支持全部导出，导出所有数据
    List<ExportProductInventoryMdmDTO.DataDTO.ContentDTO> exportProductInventoryMdmList;
    JSONObject firstJsonObject =
        httpUtil.getProductInventoryManagementPage(exportProductParams.getMdmCode(),
            exportProductParams.getPlatformCode(), null, null,
            null, 1, 1);
    ExportProductInventoryMdmDTO
        fistResult = JSONObject.toJavaObject(firstJsonObject, ExportProductInventoryMdmDTO.class);
    if (fistResult == null || fistResult.getCode() != ResultBean.SUCCESS
        || fistResult.getData() == null || (fistResult.getData().getTotalCount() != null
        && fistResult.getData().getTotalCount() == 0)) {
      throw new CheckException("查询的区域库存数据为空");
    }
    //所有数据的个数
    Integer totalCount = fistResult.getData().getTotalCount();
    //限制查询个数
    int batchSize = 1000;
    List<ProductStockDTO> dtoList = new ArrayList<>();
    if (totalCount < batchSize) {
      JSONObject allJsonObject =
          httpUtil.getProductInventoryManagementPage(exportProductParams.getMdmCode(),
              exportProductParams.getPlatformCode(), null, null, null, 1, totalCount);
      ExportProductInventoryMdmDTO allResult =
          JSONObject.toJavaObject(allJsonObject, ExportProductInventoryMdmDTO.class);
      if (allResult == null || allResult.getCode() != ResultBean.SUCCESS
          || allResult.getData() == null || (allResult.getData().getTotalCount() != null
          && allResult.getData().getTotalCount() == 0)) {
        throw new CheckException("查询的区域库存数据为空");
      }
      exportProductInventoryMdmList = allResult.getData().getContent();
      for (ExportProductInventoryMdmDTO.DataDTO.ContentDTO contentDTO : exportProductInventoryMdmList) {
        List<AreaInventoryDTO> areaInventoryList = contentDTO.getAreaInventoryList();
        if (CollUtil.isNotEmpty(areaInventoryList)) {
          contentDTO.getAreaInventoryList().forEach(areaInventoryDTO -> {
            ProductStockDTO dto = new ProductStockDTO();
            dto.setProductCode(contentDTO.getCode());
            dto.setProductName(contentDTO.getName());
            dto.setModel(contentDTO.getManuCode());
            dto.setUnit(contentDTO.getUnitName());
            dto.setSellArea(areaInventoryDTO.getSysRegionCodeName());
            dto.setStock(BigDecimalUtil.formatForStandard(areaInventoryDTO.getInventoryAmount())
                .toPlainString());
            dtoList.add(dto);
          });
        } else {
          ProductStockDTO dto = new ProductStockDTO();
          dto.setProductCode(contentDTO.getCode());
          dto.setProductName(contentDTO.getName());
          dto.setModel(contentDTO.getManuCode());
          dto.setUnit(contentDTO.getUnitName());
          dto.setSellArea("待填写");
          dto.setStock("待填写");
          dtoList.add(dto);
        }
      }
    } else {
      // 如果总数量大于单次查询的数量，则需要分页获取数据
      int totalBatches = (int) Math.ceil((double) totalCount / batchSize);
      for (int i = 0; i < totalBatches; i++) {
        int currentPage = i + 1;
        JSONObject pageJsonObject =
            httpUtil.getProductInventoryManagementPage(exportProductParams.getMdmCode(),
                exportProductParams.getPlatformCode(), null, null, null, currentPage, batchSize);
        ExportProductInventoryMdmDTO pageResult = JSONObject.toJavaObject(pageJsonObject, ExportProductInventoryMdmDTO.class);
        if (pageResult == null || pageResult.getCode() != ResultBean.SUCCESS
            || pageResult.getData() == null || (pageResult.getData().getTotalCount() != null
            && pageResult.getData().getTotalCount() == 0)) {
          throw new CheckException("查询的区域库存数据为空");
        }
        List<ExportProductInventoryMdmDTO.DataDTO.ContentDTO> currentPageData = pageResult.getData().getContent();
        // 处理每一批次的数据
        for (ExportProductInventoryMdmDTO.DataDTO.ContentDTO contentDTO : currentPageData) {
          List<AreaInventoryDTO> areaInventoryList = contentDTO.getAreaInventoryList();
          if (CollUtil.isNotEmpty(areaInventoryList)) {
            for (AreaInventoryDTO inventoryDTO : areaInventoryList) {
              ProductStockDTO dto = new ProductStockDTO();
              dto.setProductCode(contentDTO.getCode());
              dto.setProductName(contentDTO.getName());
              dto.setModel(contentDTO.getManuCode());
              dto.setUnit(contentDTO.getUnitName());
              dto.setSellArea(inventoryDTO.getSysRegionCodeName());
              dto.setStock(BigDecimalUtil.formatForStandard(inventoryDTO.getInventoryAmount()).toPlainString());
              dtoList.add(dto);
            }
          } else {
            ProductStockDTO dto = new ProductStockDTO();
            dto.setProductCode(contentDTO.getCode());
            dto.setProductName(contentDTO.getName());
            dto.setModel(contentDTO.getManuCode());
            dto.setUnit(contentDTO.getUnitName());
            dto.setSellArea("待填写");
            dto.setStock("待填写");
            dtoList.add(dto);
          }
        }
      }
    }
    return dtoList;
  }
}

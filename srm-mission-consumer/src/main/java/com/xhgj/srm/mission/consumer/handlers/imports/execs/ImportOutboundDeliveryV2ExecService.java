package com.xhgj.srm.mission.consumer.handlers.imports.execs;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormCallStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormReviewStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.utils.DateUtil;
import com.xhgj.srm.jpa.entity.InventoryLocation;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderProductV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.jpa.repository.InventoryLocationRepository;
import com.xhgj.srm.mission.common.MissionDispatchParam;
import com.xhgj.srm.mission.consumer.framework.MissionCompleteResult;
import com.xhgj.srm.mission.consumer.framework.service.FcMissionService;
import com.xhgj.srm.mission.consumer.handlers.ExecService;
import com.xhgj.srm.mission.consumer.handlers.imports.ImportMissionCompleteResult;
import com.xhgj.srm.mission.consumer.handlers.imports.execs.params.ImportOutboundDeliveryV2Param;
import com.xhgj.srm.request.utils.DownloadThenUpUtil;
import com.xhgj.srm.v2.repository.SupplierOrderDetailV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderProductV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderToFormV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderV2Repository;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * ImportOutboundDeliveryV2ExecService
 */
@Service
@Slf4j
public class ImportOutboundDeliveryV2ExecService implements ExecService {
  @Resource
  private DownloadThenUpUtil downloadThenUpUtil;
  @Resource
  private SupplierOrderV2Repository supplierOrderV2Repository;
  @Resource
  private SupplierOrderDetailV2Repository supplierOrderDetailV2Repository;
  @Resource
  private SupplierOrderToFormV2Repository supplierOrderToFormV2Repository;
  @Resource
  private SupplierOrderProductV2Repository supplierOrderProductV2Repository;
  @Resource
  private FcMissionService fcMissionService;
  @Resource
  private ApplicationContext applicationContext;
  @Resource
  private InventoryLocationRepository inventoryLocationRepository;

  @Override
  public MissionCompleteResult exec(Collection<?> collection, MissionDispatchParam dispatchParam) {
    // 转换为JSONObject
    JSONObject jsonObject = JSONObject.parseObject(dispatchParam.getParams());
    // 获取filePath
    String filePath = jsonObject.getString("filePath");
    // 获取fileName
    String fileName = jsonObject.getString("fileName");
    // collection转换为SupplierImportParam
    List<ImportOutboundDeliveryV2Param> importOutboundDeliveryParamList = (List<ImportOutboundDeliveryV2Param>) collection;
    // 执行导入
    Iterator<ImportOutboundDeliveryV2Param> iterator = importOutboundDeliveryParamList.iterator();
    int rowIndex = 1;
    int successCount = 0;
    while (iterator.hasNext()) {
      ImportOutboundDeliveryV2Param param = iterator.next();
      try {
        // 校验必填项
        checkImportValue(param);
        SupplierOrderV2 supplierOrder = checkSupplierOrder(param);
        //入库单的详情明细
        List<SupplierOrderDetailV2> warehousingDetailList =
            checkSupplierOrderProduct(param, supplierOrder);
        ImportOutboundDeliveryV2ExecService proxy =
            applicationContext.getBean(ImportOutboundDeliveryV2ExecService.class);
        proxy.saveOneTransactional(warehousingDetailList,supplierOrder,param);

        fcMissionService.createMissionDetail(dispatchParam.getMissionId(),
            String.format("第【%s】行", rowIndex), StrUtil.EMPTY);
        successCount++;
        iterator.remove();
      } catch (Exception e) {
        log.error("导入退库单V2信息异常,第【{}】行数据写入失败,异常信息为:", rowIndex, e);
        fcMissionService.createMissionDetail(dispatchParam.getMissionId(),
            String.format("第【%s】行,", rowIndex), e.getMessage());
      }
      rowIndex++;
    }
    return ImportMissionCompleteResult.builder()
        .successCount(successCount)
        .fileName(fileName)
        .filePath(filePath)
        .build();
  }

  @Transactional(rollbackFor = Exception.class)
  public void saveOneTransactional(List<SupplierOrderDetailV2> list, SupplierOrderV2 supplierOrder,
      ImportOutboundDeliveryV2Param param) {
    SupplierOrderToFormV2 entity =
        supplierOrderToFormV2Repository.getFirstByTypeAndSupplierOrderIdAndProductVoucherAndState(
            SupplierOrderFormType.RETURN.getType(), supplierOrder.getId(),
            param.getProductVoucherNo(), Constants.STATE_OK);
    if (entity == null) {
      entity = new SupplierOrderToFormV2();
      entity.setSupplierOrderId(supplierOrder.getId());
      entity.setType(SupplierOrderFormType.RETURN.getType());
      //冲销状态,8：已冲销，9：冲销中，10：未冲销
      entity.setStatus(
          StrUtil.equals(param.getReversalStatus(), "10") ? "5" : param.getReversalStatus());
      entity.setTime(DateUtil.convertDateTimeToLong(param.getReturnTime()));
      entity.setReturnReason(param.getRetreatReason());
      entity.setReturnWarehouse(param.getRetreatWarehouseCode());
      if (StrUtil.isNotBlank(param.getRetreatWarehouseCode())) {
        InventoryLocation inventoryLocation =
            inventoryLocationRepository.findFirstByGroupCodeAndWarehouseAndState(
                    supplierOrder.getGroupCode(), param.getRetreatWarehouseCode(), Constants.STATE_OK)
                .orElse(null);
        if (inventoryLocation != null) {
          entity.setWarehouseCode(inventoryLocation.getWarehouse());
          entity.setWarehouseName(inventoryLocation.getWarehouseName());
        }
      }
      entity.setExecutionStatus(param.getWarehouseExecuteStatus());
      //物流公司
      entity.setLogisticsCode(param.getLogisticsCompanyCode());
      entity.setLogisticsCompany(param.getLogisticsCompany());
      entity.setTrackNum(param.getTrackNum());
      entity.setConsignee(param.getRecipient());
      entity.setReceiveAddress(param.getRecipientAddress());
      entity.setNeedRedTicket(
          StrUtil.equals("是", param.getNeedRedInvoice()) ? Constants.NEED_RED_TICKET
              : Constants.STATE_DELETE);
      entity.setProductVoucher(param.getProductVoucherNo());
      entity.setSapReturnNumber(param.getSapPurchaseOrderNo());
      entity.setCreateTime(System.currentTimeMillis());
      entity.setUpdateTime(System.currentTimeMillis());
      entity.setState(Constants.STATE_OK);
      entity.setFormCode(param.getFormCode());
      entity.setReviewStatus(SupplierOrderFormReviewStatus.NORMAL.getCode());
      entity.setReviewTime(DateUtil.convertDateTimeToLong(param.getReturnTime()));
      entity.setCallStatus(SupplierOrderFormCallStatus.NO_CALL.getStatus());
      entity.setPostingDate(System.currentTimeMillis());
      supplierOrderToFormV2Repository.saveAndFlush(entity);
    }

    //入库单的详情明细组装退库单明细
    for (SupplierOrderDetailV2 orderDetail : list) {
      SupplierOrderDetailV2 supplierOrderDetail = new SupplierOrderDetailV2();
      supplierOrderDetail.setSortNum(NumberUtil.parseInt(param.getSapRwoId()));
      supplierOrderDetail.setSapRowId(param.getSapProductVoucherLineItem());
      supplierOrderDetail.setOrderToFormId(entity.getId());
      //总账科目
      SupplierOrderProductV2 supplierOrderProduct = orderDetail.getSupplierOrderProduct();
      supplierOrderProduct.setLedgerSubjectCode(param.getLedgerSubjectCode());
      supplierOrderProduct.setLedgerSubject(param.getLedgerSubjectCode() + param.getLedgerSubjectName());
      supplierOrderProductV2Repository.saveAndFlush(supplierOrderProduct);
      supplierOrderDetail.setOrderProductId(orderDetail.getOrderProductId());
      supplierOrderDetail.setSupplierOrderProduct(supplierOrderProduct);
//      supplierOrderDetail.setSortNum(orderDetail.getSortNum());
      SupplierOrderDetailV2 orderDetailBase = orderDetail.getDetailed();
      if (orderDetailBase == null) {
        continue;
      }
      supplierOrderDetail.setDetailedId(orderDetailBase.getId());
      supplierOrderDetail.setStockOutputQty(NumberUtil.toBigDecimal(param.getStockOutputQty()));
      supplierOrderDetail.setReturnQty(NumberUtil.toBigDecimal(param.getStockOutputQty()));
      //对应入库单入库数量
      supplierOrderDetail.setStockInputQty(NumberUtil.toBigDecimal(param.getStockInputQty()));
      //已开红票数量
      supplierOrderDetail.setInvoicableNum(NumberUtil.toBigDecimal(param.getRedInvoiceQty()));
      supplierOrderDetail.setReturnPrice(NumberUtil.toBigDecimal(param.getReturnPrice()));
      supplierOrderDetail.setReturnAmount(NumberUtil.toBigDecimal(param.getReturnAmount()));
      supplierOrderDetail.setBatchNo(param.getBatchNumber());
      //20250416 产品需要去掉校验逻辑
      //      BigDecimal subtract =
      //          orderDetail.getStockInputQty().subtract(NumberUtil.add(NumberUtil.toBigDecimal(param.getStockOutputQty()),
      //              orderDetail.getStockOutputQty()));
      //      if (NumberUtil.isLess(subtract,BigDecimal.ZERO)) {
      //        throw new CheckException("退库数量超过可退货数量");
      //      }
      supplierOrderDetail.setTotalPrice(orderDetail.getPrice().multiply(supplierOrderDetail.getStockOutputQty()));
      supplierOrderDetail.setPrice(orderDetailBase.getPrice());
      //退库单添加采购订单id
      supplierOrderDetail.setPurchaseOrderId(supplierOrder.getId());
      supplierOrderDetail.setOpenRedInvoice(
          StrUtil.equals("是", param.getNeedRedInvoice()) ? true : false);
      supplierOrderDetail.setState(Constants.STATE_OK);
      supplierOrderDetail.setCreateTime(System.currentTimeMillis());
      supplierOrderDetail.setUpdateTime(System.currentTimeMillis());
      supplierOrderDetail.setOrderToFormType(SupplierOrderFormType.RETURN.getType());
      supplierOrderDetailV2Repository.save(supplierOrderDetail);
    }
  }

  private List<SupplierOrderDetailV2> checkSupplierOrderProduct(ImportOutboundDeliveryV2Param param
      ,SupplierOrderV2 supplierOrder) {
    List<String > supplierOrderToFormIdList =
        supplierOrderToFormV2Repository.getAllByTypeAndSupplierOrderIdAndStateOrderByTimeAsc(
            SupplierOrderFormType.WAREHOUSING.getType(), supplierOrder.getId(),
            Constants.STATE_OK).stream().map(SupplierOrderToFormV2::getId).collect(Collectors.toList());
    if (CollUtil.isEmpty(supplierOrderToFormIdList)) {
      throw new CheckException("未找到有效的入库单信息");
    }
    // 查询该采购订单下面的入库单
    List<SupplierOrderDetailV2> detailList =
        supplierOrderDetailV2Repository.findByOrderToFormIdInAndState(supplierOrderToFormIdList, Constants.STATE_OK);
    if (CollUtil.isEmpty(detailList)) {
      throw new CheckException("未找到有效的入库单详情信息");
    }
    List<SupplierOrderDetailV2> resultList = detailList.stream()
        .filter(detail -> detail.getSupplierOrderProduct() != null) // 检查supplierOrderProduct是否为空
        .filter(detail -> StrUtil.equals(detail.getSupplierOrderProduct().getCode(), param.getProductCode()))
        .collect(Collectors.toList());
    if (CollUtil.isEmpty(resultList)) {
      throw new CheckException("通过物料编码【" + param.getProductCode() + "】找不到相关物料信息，可能有脏数据!");
    }
    return resultList;
  }


  private SupplierOrderV2 checkSupplierOrder(ImportOutboundDeliveryV2Param param) {
    SupplierOrderV2 supplierOrder = Optional.ofNullable(
            supplierOrderV2Repository.findFirstByCodeAndState(param.getOrderCode(), Constants.STATE_OK))
        .orElseThrow(() -> new CheckException("通过采购订单号【" + param.getOrderCode()
            + "】找不到供应商、采购员、采购部门、订单类型，可能有脏数据!"));
    if (StrUtil.hasBlank(supplierOrder.getSupplierName(), supplierOrder.getPurchaseMan(),
        supplierOrder.getPurchaseDept(), supplierOrder.getOrderType())) {
      throw new CheckException("采购订单号【" + param.getOrderCode()
          + "】的供应商、采购员、采购部门、订单类型不能为空，可能有脏数据!");
    }
    return supplierOrder;
  }

  private void checkImportValue(ImportOutboundDeliveryV2Param param) {
    // 校验必填项
    if (StrUtil.hasBlank(param.getOrderCode(), param.getReturnTime(), param.getRetreatReason(),
        param.getOrderCode(), param.getWarehouseExecuteStatus(), param.getRecipient(), param.getNeedRedInvoice(), param.getProductVoucherNo(),
        param.getSapPurchaseOrderNo(), param.getProductCode(), param.getStockOutputQty(),
        param.getStockInputQty(), param.getRedInvoiceQty(), param.getReturnPrice(),
        param.getReturnAmount(), param.getBatchNumber(), param.getReversalStatus(),
        param.getReviewStatus(),param.getSapRwoId())) {
      throw new CheckException("必填项不能为空");
    }
    if (!ListUtil.toList("0", "1", "2").contains(param.getWarehouseExecuteStatus())) {
      throw new CheckException("仓库执行状态填写错误！");
    }
    if (!ListUtil.toList("8", "9", "10").contains(param.getReversalStatus())) {
      throw new CheckException("冲销状态填写错误！");
    }
    if (!ListUtil.toList("是", "否").contains(param.getNeedRedInvoice())) {
      throw new CheckException("是否需要开红票填写错误！");
    }
    if (!"通过".equals(param.getReviewStatus())) {
      throw new CheckException("审批状态只能填写通过！");
    }
    BigDecimal stockOutputQty =   Convert.toBigDecimal(param.getStockOutputQty());
    if (stockOutputQty != null && stockOutputQty.scale() > 3) {
      throw new CheckException("退库数量数量最大填写三位小数!");
    }
    BigDecimal stockInputQty =   Convert.toBigDecimal(param.getStockInputQty());
    if (stockInputQty != null && stockInputQty.scale() > 3) {
      throw new CheckException("对应入库单入库数量最大填写三位小数!");
    }
    BigDecimal redInvoiceQty =   Convert.toBigDecimal(param.getRedInvoiceQty());
    if (redInvoiceQty != null && redInvoiceQty.scale() > 2) {
      throw new CheckException("已开红票数量最大填写两位小数!");
    }
    BigDecimal returnAmount =   Convert.toBigDecimal(param.getReturnAmount());
    if (returnAmount != null && returnAmount.scale() > 6) {
      throw new CheckException("退库金额最大填写六位小数!");
    }

  }

  @Override
  public Collection<?> prepare(MissionDispatchParam dispatchParam) {
    // 转换为JSONObject
    JSONObject jsonObject = JSONObject.parseObject(dispatchParam.getParams());
    // 获取用户id
    String userId = jsonObject.getString("userId");
    // 获取filePath
    String filePath = jsonObject.getString("filePath");
    // 获取fileName
    String fileName = jsonObject.getString("fileName");
    // oss下载文件
    List<ImportOutboundDeliveryV2Param> result = new ArrayList<>();
    try ( InputStream inputStream = downloadThenUpUtil.getInputStreamFromOSS(filePath);
        Workbook book = ExcelUtil.buildByFile(fileName, inputStream);) {
      if (book == null) {
        throw new CheckException("文件为空");
      }
      // 获取sheet
      Sheet sheet = book.getSheetAt(0);
      // 获取总行数
      int lastRowNum = sheet.getPhysicalNumberOfRows();
      // 如果总行数小于1则抛出异常
      if (lastRowNum < 1) {
        throw new CheckException("导入数据为空");
      }
      // 从第三行开始读取
      int startRow = 2;
      // 读取数据
      for (int i = startRow; i <= lastRowNum; i++) {
        // 获取当前行
        Row row = sheet.getRow(i);
        if (row == null) {
          continue;
        }
        int cellNum = 0;
        // 获取固定列
        // 采购订单号
        String orderCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 采购订单物料行ID
        String sapRwoId = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 退库时间
        String returnTime = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 退库单号
        String formCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 退库原因
        String retreatReason = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 退货仓库编码
        String retreatWarehouseCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        //审批状态
        String reviewStatus = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        //仓库执行状态 0 仓库未审批未执行 1仓库未审批未执行 2无需仓库执行
        String warehouseExecuteStatus = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        String logisticsCompanyCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        String logisticsCompany = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        String trackNum = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        String recipient = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        String recipientAddress = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        String needRedInvoice = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        String productVoucherNo = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        String sapPurchaseOrderNo = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        String sapProductVoucherLineItem = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        String productCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        String stockOutputQty = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        String stockInputQty = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        String redInvoiceQty = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        String returnPrice = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        String returnAmount = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        String batchNumber = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        //总账科目编码
        String ledgerSubjectCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        //总账科目名称
        String ledgerSubjectName = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        String reversalStatus = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        ImportOutboundDeliveryV2Param one =
            ImportOutboundDeliveryV2Param.builder().orderCode(orderCode).sapRwoId(sapRwoId).returnTime(returnTime).formCode(formCode)
                .retreatReason(retreatReason).retreatWarehouseCode(retreatWarehouseCode)
                .warehouseExecuteStatus(warehouseExecuteStatus).logisticsCompanyCode(logisticsCompanyCode).logisticsCompany(logisticsCompany).trackNum(trackNum).recipient(recipient)
                .recipientAddress(recipientAddress).needRedInvoice(needRedInvoice).productVoucherNo(productVoucherNo).sapPurchaseOrderNo(sapPurchaseOrderNo)
                .sapProductVoucherLineItem(sapProductVoucherLineItem).productCode(productCode).stockOutputQty(stockOutputQty).stockInputQty(stockInputQty)
                .redInvoiceQty(redInvoiceQty).returnPrice(returnPrice).returnAmount(returnAmount).batchNumber(batchNumber)
                .reversalStatus(reversalStatus).reviewStatus(reviewStatus).ledgerSubjectCode(ledgerSubjectCode).ledgerSubjectName(ledgerSubjectName)
                .userId(userId).build();
        result.add(one);
      }
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
    if (CollUtil.isEmpty(result)) {
      throw new CheckException("导入数据为空");
    }
    return result;
  }
}

package com.xhgj.srm.mission.consumer.handlers.imports.execs.params.purchaseOrder;/**
 * @since 2025/3/12 14:42
 */

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.SettleCurrency;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *<AUTHOR>
 *@date 2025/3/12 14:42:11
 *@description 期初采购订单导入DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PurchaseOrderImportV2DTO {
  /**
   * 用于确认唯一一条productId
   */
  private String productId;


  /**
   * 订单号
   */
  private String code;

  /**
   * 物料明细行id
   */
  private Integer detailRowId;

  /**
   * 执行
   */
  private Integer rowIndex;

  /**
   * 订单状态 (待履约、履约中、已完成)
   */
  private String orderState;

  /**
   * 订单状态编码
   */
  private String orderStateCode;

  /**
   * 订单创建时间
   */
  private String createTimeStr;

  /**
   * 创建时间时间戳
   * 格式 2023-03-12 14:42:11
   */
  private Long createTime;

  /**
   * 采购订单类型
   */
  private String orderTypeStr;

  /**
   * 采购订单类型枚举
   */
  private String orderType;

  /**
   * 采购组织编码
   */
  private String groupCode;

  /**
   * 采购部门编码
   */
  private String departmentCode;

  /**
   * 采购员
   * 如 1188张三 由前4位工号 + 姓名组成
   */
  private String purchaser;

  /**
   * 供应商主数据编码
   */
  private String mdmCode;

  /**
   * 开票方主数据编码
   */
  private String invoiceMdmCode;

  /**
   * 货币码
   * 请输入：人民币、美元、英镑、日元、新台币、欧元、港币
   */
  private String currency;

  /**
   * 货币码 code
   */
  private String currencyCode;

  /**
   * 发票类型
   * 请输入：增值税专用发票、普通发票、其他
   */
  private String invoiceType;

  /**
   * 发票类型 code
   */
  private String invoiceTypeCode;

  /**
   * 是否亏本
   */
  private String loss;

  /**
   * 是否走scp
   */
  private String scp;


  /**
   * 订单备注
   */
  private String orderRemark;

  /**
   * 供方联系人
   */
  private String supplierContact;

  /**
   * 供方联系人电话
   */
  private String supplierContactPhone;

  /**
   * 供方联系人邮箱
   */
  private String supplierContactEmail;

  /**
   * 供方联系人传真
   */
  private String supplierContactFax;

  /**
   * 收件信息 收件人
   */
  private String recipientName;

  /**
   * 收件信息 收件人电话
   */
  private String recipientPhone;

  /**
   * 收件信息 收件人地址
   */
  private String recipientAddress;

  /**
   * 订货金额
   */
  private String orderAmount;

  /**
   * 已开票数量
   */
  private String invoiceQty;

  /**
   * 物料编码
   */
  private String productCode;

  /**
   * 物料名称
   */
  private String productName;

  /**
   * 物料单位
   */
  private String productUnit;

  /**
   * 物料规格
   */
  private String specification;

  /**
   * 物料型号
   */
  private String model;

  /**
   * 订货数量
   */
  private String orderQty;

  /**
   * 仓库编码
   */
  private String warehouseCode;

  /**
   * 含税单价
   */
  private String taxPrice;

  /**
   * 税率
   */
  private String taxRate;

  /**
   * 结算单价
   */
  private String settlementPrice;

  /**
   * 物料组编码
   */
  private String materialGroupCode;

  /**
   * 物料组名称
   */
  private String materialGroupName;

  /**
   * 科目分配类别编码
   */
  private String subjectAllocationCategoryCode;

  /**
   * 科目分配类别名称
   */
  private String subjectAllocationCategoryName;

  /**
   * 总账科目编码
   */
  private String generalLedgerAccountCode;

  /**
   * 总账科目名称
   */
  private String generalLedgerAccountName;

  /**
   * 成本中心编码
   */
  private String costCenterCode;

  /**
   * 成本中心名称
   */
  private String costCenterName;

  /**
   * 订单编码
   */
  private String orderCode;

  /**
   * 订单名称
   */
  private String orderName;

  /**
   * 资质卡片编码
   */
  private String profileCardCode;

  /**
   * 资质卡片名称
   */
  private String profileCardName;

  /**
   * 是否质检
   */
  private String qualityCheck;

  /**
   * 约定交货日期
   */
  private String deliveryDateStr;

  /**
   * 约定交货日期
   */
  private Long deliveryDate;

  /**
   * 实际交货日期
   */
  private String actualDeliveryDateStr;


  /**
   * 实际交货日期
   */
  private Long actualDeliveryDate;

  /**
   * 待发数量
   */
  private String pendingQty;

  /**
   * 已发数量
   */
  private String deliveredQty;

  /**
   * 取消订货数量
   */
  private String cancelOrderQty;

  /**
   * 入库数量
   */
  private String inStockQty;

  /**
   * 退库数量
   */
  private String outStockQty;

  /**
   * 剩余入库数量
   */
  private String remainingInStockQty;

  /**
   * 实际交货数量
   */
  private String actualDeliveryQty;

  /**
   * 备注
   */
  private String remark;

  /**
   * 是否免费
   * 是、否
   */
  private String isFree;

  /**
   * 项目类型
   * 标准、寄售、外协
   */
  private String projectType;

  /**
   * 项目类型编码
   */
  private String projectTypeCode;

  /**
   * 项目编码
   */
  private String projectNo;

  /**
   * 销售订单号
   */
  private String salesOrderNo;

  /**
   * 销售订单行id
   */
  private String salesOrderLineId;

  /**
   * 是否金蝶已入库
   * 是、否
   */
  private String jinDieInStock;

  /**
   * 是否已经失败
   */
  private Boolean isFailed;

  /**
   * 失败原因
   */
  private String failedReason;

  public Long getCreateTime() {
    // 创建时间转换为时间戳
    if (StrUtil.isBlank(createTimeStr)) {
      return null;
    }
    return DateUtil.parse(createTimeStr, "yyyy-MM-dd HH:mm:ss").getTime();
  }

  public String getCurrencyCode() {
    return SettleCurrency.fromInitialName(currency);
  }

  public Long getDeliveryDate() {
    // 创建时间转换为时间戳
    if (StrUtil.isBlank(deliveryDateStr)) {
      return null;
    }
    return DateUtil.parse(deliveryDateStr, "yyyy-MM-dd HH:mm:ss").getTime();
  }

  public Long getActualDeliveryDate() {
    // 创建时间转换为时间戳
    if (StrUtil.isBlank(actualDeliveryDateStr)) {
      return null;
    }
    return DateUtil.parse(actualDeliveryDateStr, "yyyy-MM-dd HH:mm:ss").getTime();
  }

  public String getOrderStateCode() {
    return SupplierOrderState.findKeyByValue(this.orderState);
  }
}

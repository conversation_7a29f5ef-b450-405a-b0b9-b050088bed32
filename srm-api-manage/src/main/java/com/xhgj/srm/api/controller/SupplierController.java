package com.xhgj.srm.api.controller;

import com.xhgj.srm.api.annotations.PermissionCheck;
import com.xhgj.srm.api.dto.PurchaseUserVo;
import com.xhgj.srm.api.dto.SupplierBlackPageDataDTO;
import com.xhgj.srm.api.dto.UpdateSupplierManagerParam;
import com.xhgj.srm.api.dto.supplier.DeleteProvisionalSupplierParam;
import com.xhgj.srm.api.dto.supplier.ExportSupplierInGroupParams;
import com.xhgj.srm.api.dto.supplier.InteriorSupplierPageDTO;
import com.xhgj.srm.api.dto.supplier.ProvisionalSupplierPageDTO;
import com.xhgj.srm.api.dto.supplier.ProvisionalSupplierPageQuery;
import com.xhgj.srm.api.dto.supplier.ProvisionalSupplierParam;
import com.xhgj.srm.api.dto.supplier.SupplierChinaDTO;
import com.xhgj.srm.api.dto.supplier.SupplierChinaQuery;
import com.xhgj.srm.api.dto.supplier.SupplierExportParam;
import com.xhgj.srm.api.dto.supplier.SupplierMainDataAbroadDTO;
import com.xhgj.srm.api.dto.supplier.SupplierMainDataChinaDTO;
import com.xhgj.srm.api.dto.supplier.SupplierPersonDTO;
import com.xhgj.srm.api.dto.supplier.SupplierPersonQuery;
import com.xhgj.srm.api.dto.supplier.UpdatePartnerDTO;
import com.xhgj.srm.api.dto.supplier.change.SupplerChangeInfoDTO;
import com.xhgj.srm.api.dto.workbench.MySupplierData;
import com.xhgj.srm.api.service.SupplierInGroupService;
import com.xhgj.srm.api.service.SupplierService;
import com.xhgj.srm.api.service.XhgjService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.request.dto.partner.BusinessInfoDTO;
import com.xhgj.srm.request.dto.partner.PartnerIcpDTO;
import com.xhgj.srm.service.ShareSupplierService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.web.dto.param.PageParam;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import com.xhiot.boot.repeat.annotation.RepeatSubmit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("supplier")
@Api(tags = {"供应商管理接口"})
@Validated
@Slf4j
public class SupplierController extends AbstractRestController {

  @Autowired
  private SupplierService supplierService;

  @Autowired
  private SupplierInGroupService supplierInGroupService;
  @Resource
  private ShareSupplierService shareSupplierService;

  @Resource
  private XhgjService xhgjService;

  @ApiOperation("后台分页获取国内供应商列表")
  @GetMapping("/getSupplierChinaByPage")
  public ResultBean<PageResult<SupplierChinaDTO>> getSupplierChinaByPage(@Valid SupplierChinaQuery query) {
    return new ResultBean<>(supplierInGroupService.getSupplierDomesticByPage(query));
  }

  @ApiOperation("分页获取供应商名称和主数据编码列表（模糊查询）")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "enterpriseName", value = "供应商名称"),
      @ApiImplicitParam(name = "enterpriseCode", value = "供应商主数据编码"),
      @ApiImplicitParam(name = "erpCode", value = "供应商erpcode"),
      @ApiImplicitParam(name = "param", value = "分页参数", required = true)
  })

  @GetMapping("/getSupplierDomesticByPage")
  public ResultBean<PageResult<Object>> getSupplierDomesticByPage(
      String enterpriseName, String erpCode,String enterpriseCode, @Valid PageParam param) {
    log.error("接口迁移至portal");
    throw new CheckException("接口迁移至portal");
  }

  @ApiOperation("获取主数据编码")
  @ApiImplicitParam(name = "supplierId", value = "供应商 Id", required = true)
  @GetMapping("/getCodeBySupplierId")
  public ResultBean<String> getCodeBySupplierId(@RequestParam @NotBlank String supplierId) {
    return new ResultBean<>(
        supplierService.getCodeBySupplierId(supplierId));
  }

  @ApiOperation("获取供应商id根据MDM主数据编码")
  @ApiImplicitParam(name = "MDMCode", value = "主数据编码", required = true)
  @GetMapping("/getIdByMDMCode")
  public ResultBean<String> getIdByMDMCode(@RequestParam @NotBlank String MDMCode) {
    log.error("接口迁移至portal");
    throw new CheckException("接口迁移至portal");
  }

  @ApiOperation("后台分页获取个人供应商列表")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "query", value = "查询参数", required = true),
      @ApiImplicitParam(name = "param", value = "分页参数", required = true)
  })
  @GetMapping("/getSupplierPersonByPage")
  public ResultBean<PageResult<SupplierPersonDTO>> getSupplierPersonByPage(
      @Valid SupplierPersonQuery query, @Valid PageParam param) {
    return new ResultBean<>(supplierInGroupService.getSupplierPersonByPage(query, param));
  }

  @ApiOperation(value = "修改供应商erp信息", notes = "修改供应商erp信息")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "supplierId", value = "供应商id", required = true),
      @ApiImplicitParam(name = "erpId", value = "供应商erpid"),
      @ApiImplicitParam(name = "erpCode", value = "供应商erpcode")
  })
  @PostMapping("/updateERPInfo")
  public ResultBean<Boolean> updateERPInfo(
      @RequestParam("supplierId") String supplierId, String erpId, String erpCode) {
    supplierService.updateERPInfo(supplierId, erpId, erpCode);
    return new ResultBean<>(true, "操作成功!");
  }

  @ApiOperation(value = "推送供应商信息同步至erp", notes = "推送供应商信息同步至erp")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "supplierIds", value = "供应商id", required = true),
      @ApiImplicitParam(name = "type", value = "供应商类型", required = true),
  })
  @PostMapping("/pushToErp")
  public ResultBean<Boolean> pushToErp(
      @RequestParam String supplierIds, @RequestParam(defaultValue = "1") String type) {
    supplierService.pushToErp(supplierIds, type);
    return new ResultBean<>(true, "操作成功!");
  }

  /**
   * Title：获取供应商黑名单列表
   *
   * <p>Description:
   *
   * <p>
   *
   * @date 2019年8月13日 下午2:58:40
   */
  @ApiOperation(value = "获取供应商黑名单列表", notes = "获取供应商黑名单列表")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "mdmCode", value = "mdm编码"),
      @ApiImplicitParam(name = "enterpriseName", value = "供应商名称"),
      @ApiImplicitParam(name = "enterpriseLevel", value = "供应商等级"),
      @ApiImplicitParam(name = "userGroup", value = "使用组织"),
      @ApiImplicitParam(name = "enterpriseNature", value = "供应商性质"),
      @ApiImplicitParam(name = "industry", value = "行业"),
      @ApiImplicitParam(name = "purchaserName", value = "采购人"),
      @ApiImplicitParam(name = "brands", value = "品牌"),
      @ApiImplicitParam(name = "mobile", value = "联系方式"),
      @ApiImplicitParam(name = "contacts", value = "联系人"),
      @ApiImplicitParam(name = "schemeId", value = "方案id"),
      @ApiImplicitParam(name = "pageNo", value = "当前页"),
      @ApiImplicitParam(name = "pageSize", value = "每页展示数量"),
  })
  @GetMapping("/getSupplierBlackPage")
  public ResultBean<PageResult<SupplierBlackPageDataDTO>> getSupplierBlackPage(
      String mdmCode,
      String enterpriseName,
      String enterpriseLevel,
      String userGroup,
      String enterpriseNature,
      String industry,
      String purchaserName,
      String brands,
      String mobile,
      String contacts,
      String schemeId,
      String userId,
      @RequestParam(defaultValue = "1") Integer pageNo,
      @RequestParam(defaultValue = "10") Integer pageSize) {
    return new ResultBean<>(
        supplierService.getSupplierBlackPage(
            mdmCode,
            enterpriseName,
            enterpriseLevel,
            userGroup,
            enterpriseNature,
            industry,
            purchaserName,
            brands,
            mobile,
            contacts,
            schemeId,
            userId,
            pageNo,
            pageSize));
  }

  /**
   * @Title: 拉黑供应商 @Description:
   * <AUTHOR>
   * @date 2021/6/3 14:36
   */
  @ApiOperation(value = "拉黑供应商", notes = "拉黑供应商")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "userGroup", value = "组织编码", required = true),
      @ApiImplicitParam(name = "supplierInGroupId", value = "供应商id", required = true),
      @ApiImplicitParam(name = "userId", value = "操作人id", required = true),
      @ApiImplicitParam(name = "reason", value = "拉黑原因")
  })
  @PostMapping(value = "/blockSupplierById")
  public ResultBean<Boolean> blockSupplierById(
      @RequestParam("userGroup") String userGroup,
      @RequestParam("supplierInGroupId") String supplierInGroupId,
      String reason, @NotBlank String range) {
    User user = getUser();
    supplierService.blockSupplierById(
        validateAndGetGroup(user, userGroup), supplierInGroupId, user, reason, range);
    // es删除供应商
    // esSupplierService.deleteById(supplierIds);
    return new ResultBean<>(true, "操作成功!");
  }

  /**
   * @Title: 解除供应商 @Description:
   * <AUTHOR>
   * @date 2021/6/3 14:36
   */
  @ApiOperation(value = "解除黑名单", notes = "解除黑名单")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "supplierIds", value = "供应商id", required = true),
      @ApiImplicitParam(name = "userId", value = "用户id"),
  })
  @PostMapping("/relieveSupplierById")
  public ResultBean<?> relieveSupplierById(
      @RequestParam("supplierIds") String supplierIds, @RequestParam("userId") String userId) {
    supplierService.relieveSupplierById(supplierIds, userId);
    return new ResultBean<>(true, "操作成功!");
  }

  @ApiOperation(value = "获取最新供应商编码", notes = "获取最新供应商编码")
  @RequestMapping(value = "/getNewSupplierCode", method = RequestMethod.GET)
  public ResultBean<String> getNewSupplierCode() {
    return new ResultBean<>(supplierService.getNewSupplierCode());
  }

  @ApiOperation(value = "更新供应商同步erp状态", notes = "更新供应商同步erp状态")
  @PostMapping("/updateSupplierSynState")
  public ResultBean<Boolean> updateSupplierSynState() {
    supplierService.updateSupplierSynState();
    return new ResultBean<>(true, "操作成功!");
  }

  @ApiOperation(value = "更新供应商财务信息", notes = "更新供应商财务信息")
  @PostMapping("/updateSupplierFinancialInfo")
  public ResultBean<Boolean> updateSupplierFinancialInfo() {
    supplierService.updateSupplierFinancialInfo();
    return new ResultBean<>(true, "操作成功!");
  }

  @SneakyThrows
  @ApiOperation(value = "批量修改负责采购", notes = "批量修改负责采购")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "userId", value = "用户id"),
  })
  @PostMapping("/updateExcelSupplierManage")
  public ResultBean<Boolean> updateExcelSupplierManage(MultipartFile file, String userId) {
    supplierService.updateExcelSupplierManage(file, userId);
    return new ResultBean<>(true, "操作成功！");
  }

  @SneakyThrows
  @ApiOperation(value = "批量修改注册地址", notes = "批量修改负责采购")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "userId", value = "用户id"),
  })
  @PostMapping("/updateSupplierRegisteredAddress")
  public ResultBean<Boolean> updateSupplierRegisteredAddress(MultipartFile file, String userId) {
    supplierService.updateSupplierRegisteredAddress(file, userId);
    return new ResultBean<>(true, "操作成功！");
  }

  @SneakyThrows
  @ApiOperation(value = "批量导入供应商授权", notes = "批量修改负责采购")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "userId", value = "用户id"),
  })
  @PostMapping("/updateSupplierEmpower")
  public ResultBean<Boolean> updateSupplierEmpower(MultipartFile file, String userId) {
    supplierService.updateSupplierEmpower(file, userId);
    return new ResultBean<>(true, "操作成功！");
  }

  @ApiOperation(value = "更新供应商完善度", notes = "更新供应商完善度")
  @PostMapping("/updateSupplierScore")
  public ResultBean<Boolean> updateSupplierScore(
      @RequestParam(required = false, name = "supplierIngroupId")String supplierInGroupId
  ) {
    shareSupplierService.updateSupplierScore(supplierInGroupId);
    return new ResultBean<>(true, "操作成功!");
  }

  @ApiOperation(value = "导出国内供应商", notes = "导出国内供应商")
  @RequestMapping(value = "/exportNormalSupplier", method = RequestMethod.GET)
  public ResultBean<Boolean> exportNormalSupplier(@Valid SupplierExportParam supplierExportParam) {
    supplierService.exportNormalSupplier(supplierExportParam);
    return new ResultBean<>(true, "操作成功!");
  }

  @ApiOperation(value = "处理协议未上传供应商信息", notes = "处理协议未上传供应商信息")
  @RequestMapping(value = "/dealNoXESupplier", method = RequestMethod.GET)
  public ResultBean<Boolean> dealNoXESupplier() {
    supplierService.dealNoXESupplier();
    return new ResultBean<>(true, "操作成功!");
  }

  @ApiOperation("获取我的供应商的数据")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "userId", value = "用户 id"),
      @ApiImplicitParam(name = "userGroup", value = "组织编码 "),
  })
  @GetMapping("getMySupplierData")
  public ResultBean<MySupplierData> getMySupplierData(
      @RequestParam @NotBlank(message = "用户 id 不能为空") String userId,
      @RequestParam @NotBlank(message = "组织编码不能空") String userGroup) {
    return new ResultBean<>(supplierService.getMySupplierData(userId, userGroup));
  }

  @ApiOperation("导出国内，海外，个人供应商")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "userId", value = "用户 id"),
      @ApiImplicitParam(name = "userGroup", value = "组织编码 "),
      @ApiImplicitParam(name = "supplierInGroupIds", value = "供应商 id 集合 "),
      @ApiImplicitParam(name = "type", value = "组织类型 "),
      @ApiImplicitParam(name = "chinaQuery", value = "国内供应商查询参数"),
      @ApiImplicitParam(name = "personQuery", value = "个人供应商查询参数"),
      @ApiImplicitParam(name = "abroadQuery", value = "海外供应商查询参数"),
  })
  @PostMapping(value = "exportSupplierInGroup", consumes = MediaType.APPLICATION_JSON_VALUE)
  public ResultBean<Boolean> exportSupplierInGroup(
      @Valid @RequestBody ExportSupplierInGroupParams params) {
    supplierService.exportSupplierInGroup(
        params.getUserId(), params.getUserGroup(), params.getSupplierInGroupId(),params.getType()
        ,params.getChinaQuery(),params.getPersonQuery(),params.getAbroadQuery());
    return new ResultBean<>();
  }

  @ApiOperation(value = "导入国内，海外，个人供应商", notes = "导入国内，海外，个人供应商")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "userId", value = "用户 id", required = true),
      @ApiImplicitParam(name = "file", value = "文件", required = true)
  })
  @PostMapping("/importGroup")
  public ResultBean<Boolean> importSupplierInGroup(
      @RequestParam MultipartFile file,
      @RequestParam @NotBlank(message = "用户 id 不能为空") String userId) {
    supplierService.importSupplierInGroup(file, userId);
    return new ResultBean<>();
  }

  @ApiOperation("获取供应商变更记录")
  @GetMapping("getSupplierChangeInfo")
  public ResultBean<PageResult<SupplerChangeInfoDTO>> getSupplierChangeInfo(
      @RequestParam @NotEmpty(message = "供应商 id 不能为空") String supplierId,
      @RequestParam @NotEmpty(message = "组织编码不能空") String userGroup,
      @Valid PageParam pageParam) {
    return new ResultBean<>(
        supplierService.getSupplierChangeInfo(
            supplierId, userGroup, pageParam.getPageNo(), pageParam.getPageSize()));
  }

  @ApiOperation(value = "修改国内供应商主数据（纠错）")
  @PostMapping("updateChinaMainData")
  public ResultBean<Boolean> updateChinaMainData(
      @Valid @RequestBody SupplierMainDataChinaDTO param, @RequestParam String groupCode) {
    User user = getUser();
    supplierService.updateSupplierMainData(param, user, validateAndGetGroup(user, groupCode));
    return successBean();
  }

  @ApiOperation(value = "修改海外供应商主数据（纠错）")
  @PostMapping("updateAbroadMainData")
  public ResultBean<Boolean> updateAbroadMainData(
      @Valid @RequestBody SupplierMainDataAbroadDTO param, @RequestParam String groupCode) {
    User user = getUser();
    supplierService.updateSupplierMainData(param, user, validateAndGetGroup(user, groupCode));
    return successBean();
  }

  @ApiOperation(value = "校验供应商名称是否允许新增", notes = "新增供应商之前根据名称校验供应商是否已存在")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "name", value = "企业名称", required = true),
      @ApiImplicitParam(name = "type", value = "文件", required = true)
  })
  @GetMapping("/validateNameBeforeAdd")
  public ResultBean<Void> validateNameBeforeAdd(
      @RequestParam String name, @RequestParam String type) {
    supplierService.validateNameBeforeAdd(name, type, true);
    return new ResultBean<>();
  }

  @ApiOperation(value = "同步多组织供应商信息", notes = "同步多组织供应商信息")
  @PostMapping("/synSupplierInGroup")
  public ResultBean<Boolean> synSupplierInGroup() {
    supplierService.synSupplierInGroup();
    return new ResultBean<>(true, "操作成功!");
  }

  @ApiOperation(value = "同步多组织供应商附件", notes = "同步多组织供应商附件")
  @PostMapping("/synSupplierInGroupFile")
  public ResultBean<Boolean> synSupplierInGroupFile() {
    supplierService.synSupplierInGroupFile();
    return new ResultBean<>(true, "操作成功!");
  }

  @ApiOperation(value = "mdm 修改主数据通知到 srm ")
  @PostMapping(value = "mdmUpdateSupplierMainData", consumes = MediaType.APPLICATION_JSON_VALUE)
  public ResultBean<Boolean> mdmUpdateSupplierMainData(
      @RequestBody @Valid UpdatePartnerDTO partnerDTO) {
    supplierService.mdmUpdateSupplierMainData(partnerDTO);
    return successBean();
  }

  @ApiOperation(value = "同步天眼查")
  @ApiImplicitParams({@ApiImplicitParam(name = "supplierId", value = "供应商 id", required = true)})
  @GetMapping("sysnTianYan")
  public ResultBean<BusinessInfoDTO> sysnTianYan(
      @RequestParam @NotBlank(message = "供应商 id") String supplierId) {
    User user = getUser();
    return new ResultBean<>(supplierService.sysnTianYan(user, supplierId));
  }

  @ApiOperation(value = "获得天眼查信息")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "supplierId", value = "供应商 id"),
      @ApiImplicitParam(name = "supplierId", value = "企业名称")
  })
  @GetMapping("getTianYanInfo")
  public ResultBean<BusinessInfoDTO> getTianYanInfo(
      @RequestParam(required = false) String supplierId,
      @RequestParam(required = false) String supplierName) {
    return new ResultBean<>(supplierService.getTianYanInfo(supplierId, supplierName));
  }

  @ApiOperation(value = "获取供应商信息")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "supplierId", value = "供应商 id"),
      @ApiImplicitParam(name = "supplierId", value = "企业名称")
  })
  @GetMapping("/getSupplierInfo")
  public ResultBean<BusinessInfoDTO> getSupplierInfo(
      @RequestParam(required = false) String supplierId,
      @RequestParam(required = false) String supplierName) {
    return new ResultBean<>(supplierService.getSupplierInfo(supplierId, supplierName));
  }

  @ApiOperation(value = "同步 mdm code")
  @GetMapping("handleSupplierMdmCode")
  public ResultBean<Boolean> handleSupplierMdmCode() {
    supplierService.handleSupplierMdmCode();
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "根据平台code获取供应商履约信息的负责采购")
  @GetMapping("dockingPurchase")
  public ResultBean<List<String>> getDockingPurchase(String code) {
    log.error("接口迁移至portal");
    throw new CheckException("接口迁移至portal");
  }

  @ApiOperation(value = "新增/修改一次性供应商")
  @PostMapping(value = "saveProvisionalSupplier")
  public ResultBean<Boolean> saveProvisionalSupplier(@RequestBody @Valid ProvisionalSupplierParam param) {
    User user = getUser();
    supplierService.saveProvisionalSupplier(user, param);
    return new ResultBean<>(true, "操作成功");
  }

  @ApiOperation("分页查询一次性供应商")
  @GetMapping("getProvisionalSupplierPage")
  public ResultBean<PageResult<ProvisionalSupplierPageDTO>> getProvisionalSupplierPage(
      ProvisionalSupplierPageQuery query, @Valid PageParam param) {
    User user = getUser();
    return new ResultBean<>(
        supplierService.getProvisionalSupplierPage(user, query, param.toPageable()));
  }

  @ApiOperation(value = "删除一次性供应商")
  @PostMapping(value = "deleteProvisionalSupplier")
  public ResultBean<Boolean> deleteProvisionalSupplier(@RequestBody @Valid DeleteProvisionalSupplierParam param) {
    User user = getUser();
    supplierService.deleteProvisionalSupplier(user, param);
    return new ResultBean<>(true, "操作成功");
  }

  @ApiOperation(value = "更新供应商拉黑范围")
  @PutMapping("blockRange")
  public ResultBean<Boolean> updateBlockRange(@NotBlank String id, @NotBlank String blockRange) {
    supplierService.updateBlockRange(id, blockRange);
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "同步内部供应商")
  @GetMapping(value = "synInteriorSupplier")
  @RepeatSubmit(interval = 10000)
  public ResultBean<Boolean> synInteriorSupplier() throws Exception {
    User user = getUser();
    supplierService.synInteriorSupplier(user);
    return new ResultBean<>(true, "操作成功");
  }

  @ApiOperation("分页查询内部供应商")
  @GetMapping("getInteriorSupplierPage")
  public ResultBean<PageResult<InteriorSupplierPageDTO>> getInteriorSupplierPage(
      ProvisionalSupplierPageQuery query, @Valid PageParam param) {
    User user = getUser();
    return new ResultBean<>(
        supplierService.getInteriorSupplierPage(user, query, param.toPageable()));
  }

  @ApiOperation("查询供应商性质")
  @GetMapping("type")
  public ResultBean<Map<String, String>> getSupplierType(String supplierId, String supplierName) {
    return new ResultBean<>(supplierService.getSupplierType(supplierId, supplierName));
  }

  @ApiOperation("批量修改国内、海外、个人供应商负责人")
  @PermissionCheck(code = {Constants.ALLOW_UPDATE_STR},type = Constants.USER_PERMISSION_UPDATE_LEADER)
  @PostMapping("batchUpdateSupplierManager")
  public ResultBean<Boolean> batchUpdateSupplierManager(@RequestBody
  @Valid UpdateSupplierManagerParam param) {
    supplierInGroupService.batchUpdateSupplierManager(param,getUser());
    return new ResultBean<>(true, "操作成功");
  }
  @ApiOperation("根据名称模糊查找负责人-最多加载十条")
  @GetMapping("getPurchaseUserByName")
  public ResultBean<List<PurchaseUserVo>> getPurchaseUserByName(@RequestParam("name") String name) {
    return new ResultBean<>(supplierService.getPurchaseUserByName(name,10));
  }

  @ApiOperation("批处理刷新供应商账期和付款方式数据")
  @PostMapping("batchUpdateSupplierPayTypeData")
  @SneakyThrows
  public ResultBean<Boolean> batchUpdateSupplierPayTypeData(@RequestParam("file") MultipartFile file) {
    supplierInGroupService.batchUpdateSupplierPayTypeData(file.getInputStream());
    return new ResultBean<>(true, "操作成功");
  }

  @ApiOperation("批处理程序刷新供应商负责人")
  @PostMapping("batchUpdateSupplierLeaderData")
  @SneakyThrows
  public ResultBean<Boolean> batchUpdateSupplierLeaderData() {
    supplierInGroupService.batchUpdateSupplierLeaderData();
    return new ResultBean<>(true, "操作成功");
  }

  @ApiOperation(value = "获取供应商的网站备案信息")
  @GetMapping("getSupplierTycIprInfo")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "keyword", value = "搜索关键字（公司名称、公司id、注册号或社会统一信用代码）srm建议使用统一社会信用代码查询"),
      @ApiImplicitParam(name = "supplierId", value = "供应商id，修改时候必填"),

  })
  public ResultBean<List<PartnerIcpDTO>> getSupplierTycIprInfo(@RequestParam  String keyword,
      String  supplierId) {
    return new ResultBean<>(xhgjService.getPartnerIprByKeyWord(keyword,supplierId));
  }
}

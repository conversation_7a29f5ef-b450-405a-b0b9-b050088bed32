package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.order.BaseOrderPageDTO;
import com.xhgj.srm.api.dto.order.ExpressCompanyDTO;
import com.xhgj.srm.api.dto.order.OpenInvoiceParams;
import com.xhgj.srm.api.dto.order.OrderDetailDTO;
import com.xhgj.srm.api.dto.order.OrderInvoiceInfoDTO;
import com.xhgj.srm.api.dto.order.OrderInvoicePageQuery;
import com.xhgj.srm.api.dto.order.OrderInvoiceParams;
import com.xhgj.srm.api.dto.order.OrderPageDTO;
import com.xhgj.srm.api.dto.order.OrderPageQuery;
import com.xhgj.srm.api.dto.order.ReceivableOrderPageDTO;
import com.xhgj.srm.api.dto.order.UpdateErpOrderParams;
import com.xhgj.srm.api.dto.order.UpdateProhibitionPaymentStateParams;
import com.xhgj.srm.api.dto.order.vo.OrderProductInfoVO;
import com.xhgj.srm.api.task.DTO.OmsInvoiceStatusDTO;
import com.xhgj.srm.common.domain.ExportOrderInvoiceParams;
import com.xhgj.srm.dto.order.ExportOrderParams;
import com.xhgj.srm.dto.order.OrderDetailInvoiceDTO;
import com.xhgj.srm.jpa.dto.order.InvoiceDTO;
import com.xhgj.srm.jpa.dto.order.InvoiceStatistics;
import com.xhgj.srm.jpa.dto.order.OrderAmountStatistics;
import com.xhgj.srm.jpa.dto.order.ReceivableOrderStatistics;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/**
 * author wangdw
 */
public interface OrderService extends BootBaseService<Order, String> {

  /**
   * 分页获取订单信息
   *
   * @return
   * @deprecated
   */
  @Deprecated
  <T extends BaseOrderPageDTO> PageResult<T> getOrderPage(
      Class<T> cls, String schemeType, String userId, OrderPageQuery orderPageQuery);

  /**
   * 分页获取订单信息
   * @param orderPageQuery
   * @return
   */
  PageResult<OrderPageDTO> getOrderPage(OrderPageQuery orderPageQuery);

  /**
   * 分页获取客户回款的订单信息
   * @param orderPageQuery
   * @return
   */
  PageResult<ReceivableOrderPageDTO> getReceivableOrderPage(OrderPageQuery orderPageQuery);

  /**
   * 获取客户回款订单统计
   * @param orderPageQuery
   * @return
   */
  ReceivableOrderStatistics getReceivableOrderStatistics(OrderPageQuery orderPageQuery);


  /**
   * 获取订单详情
   *
   * @param orderId 订单id
   * @return
   */
  OrderDetailDTO getOrderDetail(String orderId);


  /**
   * 查询订单商品、发货、退货信息
   * @param orderId 订单id
   */
  OrderProductInfoVO getOrderProductInfo(String orderId);

  /**
   * 获取物流公司列表
   *
   * @param type 下单平台 必传
   */
  List<ExpressCompanyDTO> getExpressCompanyList(String type);

  /**
   * 导出供应商订单
   *
   * @param params 参数 必传
   */
  void exportOrder(ExportOrderParams params, User user);

  /**
   * 导出供应商所有订单
   */
  void exportOrderNew(String startCreateTime,String endCreateTime);

  /**
   * 获取发票申请详情
   *
   * @param orderInvoiceId 发票申请单 id 必传
   */
  OrderInvoiceInfoDTO getOrderInvoiceApplyInfo(String orderInvoiceId);

  /**
   * 修改发票信息
   *
   * @param orderInvoiceParams 参数 必传
   */
  void updateOrderInvoice(OrderInvoiceParams orderInvoiceParams);

  /**
   * 确认签收信息
   *
   * @param orderId 订单 id 必传
   */
  void confirmAcceptInfo(String orderId, String userId);

  /*
   * 导入修改订单验收单
   */
  void orderImportUpdatePlatformYsOrder(String fileName, InputStream inputStream) throws IOException, InterruptedException;


  /**
   * 设置订单的付款状态为待申请
   *
   * @param order 订单
   */
  void setAccountStatusAllow(Order order);

  /**
   * 开票
   *
   * @param openInvoiceParams 参数 必传
   */
  void openOrUpdateInvoice(OpenInvoiceParams openInvoiceParams);

  /**
   * 获取开票申请列表
   */
  PageResult<InvoiceDTO> getOrderInvoicePageRef(OrderInvoicePageQuery orderInvoicePageQuery, boolean excludeNoInvoiceOrder);

  /**
   * 获取订单发票统计信息
   * @param orderInvoicePageQuery
   * @param excludeNoInvoiceOrder
   * @return
   */
  InvoiceStatistics getOrderInvoicingRequisitionStatistics(OrderInvoicePageQuery orderInvoicePageQuery, boolean excludeNoInvoiceOrder);

  /**
   * 获取订单发票统计信息
   */
  InvoiceStatistics getOrderInvoicingRequisitionStatisticsRef(OrderInvoicePageQuery orderInvoicePageQuery, boolean excludeNoInvoiceOrder);


  /**
   * 通过订单编号获取订单的大票 列表
   *
   * @param orderNo          订单编号，必传
   * @param dockingOrderType 订单类型，必传
   */
  List<String> getOrderLargeTicketProjectNo(Order order,String orderNo, String dockingOrderType);


  /**
   * 获取所有正常状态订单的客户订单号列表
   */
  List<Order> getAllOrderNo();

  /**
   * 根据开票状态获取对应数量
   *
   * @param type 开票状态 必传
   */
  Long getInvoiceCountByType(String type);

  /**
   * 同步订单回款进度 @Auhor: liuyq @Date: 2023/2/14 9:52
   *
   * @param id          订单 id
   * @param totalPrice  大票价税合计
   * @param returnPrice 回款金额
   * @return void
   */
  void syncOrderCustomerReturnProgress(String id, BigDecimal totalPrice, BigDecimal returnPrice);

  /**
   * 根据订单 id 获取订单集合
   *
   * @param orderIdList 订单 id 集合
   */
  List<Order> getByOrderIdList(List<String> orderIdList);

  /**
   * 导出落地商订单申请
   *
   * @param exportOrderInvoiceParams 导出参数
   */
  void exportOrderInvoice(ExportOrderInvoiceParams exportOrderInvoiceParams);

  /**
   * 获取对应订单编号的所有订单 @Athor: liuyq @Date: 2023/3/10 13:46
   *
   * @param orderNo
   * @return java.util.List<com.xhgj.srm.jpa.entity.Order>
   */
  List<Order> getAllOrderByNo(String orderNo);

  /**
   * 通过订单 id 导出物料明细
   *
   * @param orderId 订单 id
   */
  void exportOrderDetailById(String orderId, User user);

  /**
   * 通过开票申请单号获取相关订单详情
   *
   * @param applicationNumber
   * @return
   */
  List<OrderDetailDTO> getOrderDetailsByApplicationNumber(String applicationNumber);

  /**
   * 根据条件获取订单信息 @Author: liuyq @Date: 2023/6/1 15:32
   *
   * @param excludeReturnProgressList 回款状态
   * @param excludeTypeList 排除的下单平台
   * @return java.util.List<com.xhgj.srm.jpa.entity.Order>
   */
  List<Order> getAllOrderByReturnProgressExcludeType(
      List<String> excludeReturnProgressList, List<String> excludeTypeList);

  /**
   * 根据订单 id 获取明细发票详情
   * @param orderId 订单 id
   * @return
   */
  OrderDetailInvoiceDTO getOrderDetailInvoiceByOrderId(String orderId);

  /**
   * 新增采购订单
   * @param id 订单 id 必传
   */
  String addErpPurchaseOrder(String id);

  /**
   * ERP 入库单
   * @param deliverId 订单 id 必传
   */
  String erpDeliverWarehousing(String deliverId);

  /**
   * ERP 退货
   * @param returnId 退货单 id
   */
  void erpReturnRef(String returnId);

  /**
   * ERP 退货
   * @param returnId 退货单 id
   */
  void erpReturn(String returnId);


  /**
   * 获取未开票并且erp采购单号 不为null的订单列表
   * @return
   */
  List<Order> getOrderListByInvoicingState();


  /**
   * 查询数仓是否存在该订单
   * @param orderNo
   * @return
   */
  Long getScOrderCount(String orderNo);

  /**
   * 根据客户订单号获取开票状态
   * @param dockingOrderNo
   * @param dockingOrderType
   * @return
   */
  OmsInvoiceStatusDTO getInvoiceStatusByOrderNo(String dockingOrderNo,String dockingOrderType);

  /**
   * 更改订单付款状态
   * @param orderNo 客户订单号
   * @param supplierName 供应商名称
   */
  void updatePaymentStatus(String orderNo, String supplierName, String paymentState);

  /**
   * 更新订单禁止付款状态
   * @param params 入参对象
   * @return
   */
  Boolean updateProhibitionPaymentState(UpdateProhibitionPaymentStateParams params);

  /**
   * 驳回签收信息
   * @return
   */
  Boolean reject(String orderId, String userId, String groundsForRejection);

  void updatePaymentState();

  /**
   * 查询对应供应商在对应的时间端内的订单，包含开始时间，不包含结束时间。
   * 参数为null时，对应条件不参与筛选。
   * @param supplierId 供应商id
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @param platformCode 下单平台编码
   */
  List<Order> getBySupplierIdAndPlatformCodeAndOrderTime(String supplierId, String platformCode,
      Long startTime, Long endTime);

  /**
   * 手动调用erp修改反审核
   */
  void updateErpOrder(UpdateErpOrderParams params);

  /**
   * 修改下单金额，暴力修改
   * @param orderId 订单id
   * @param price 下单金额
   */
  void updateOrderPrice(String orderId, BigDecimal price);

  /**
   * 修改供应商开票状态，暴力修改
   * @param orderId 订单id
   * @param supplierInvoiceState 供应商开票状态
   */
  void updateSupplierInvoiceState(String orderId, String supplierInvoiceState);

  /**
   * 撤回订单
   * @param orderId 订单id
   */
  void withdraw(String orderId);

  List<OrderProductInfoVO> batchGetOrderProductInfo(List<String> arrayList);

  void importRelationLargeTicket(MultipartFile file, String userId);

  List<com.xhgj.srm.common.dto.ExpressCompanyDTO> getLogisticsCompanies();

  boolean orderIsWarehousing(Order order);

    void dataHandle();

  void exportOrderWithProduct(ExportOrderParams orderIds, User user);

  void exportOrderDeliveryDetailProduct(ExportOrderParams param, User user);

  void exportOrderReturnDetailProduct(ExportOrderParams param, User user);

  OrderAmountStatistics getOrderAmountStatistics(OrderPageQuery param, User user);

  /**
   * 更新单个订单的回款状态、记录
   * @param order
   */
  void updateOrderPayment(Order order);

  /**
   * 处理旧数据的回款记录表
   */
  void handleOldOrderForReceitable();

  void handleOldData2();

  void handleOldData3();
}

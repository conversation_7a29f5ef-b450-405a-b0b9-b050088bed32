package com.xhgj.srm.mobile.dto;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhiot.boot.core.common.util.DateUtils;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @ClassName BusinessUpdateDto Create by Liuyq on 2021/6/1 16:06
 */
@Data
public class BusinessUpdateParam {
  @ApiModelProperty("供应商id")
  @NotBlank(message = "供应商id不能为空")
  private String supplierId;

  @ApiModelProperty("企业名称")
  @NotBlank(message = "企业名称不能为空")
  private String enterpriseName;

  @ApiModelProperty("法定代表人")
  @NotBlank(message = "法定代表人不能为空")
  private String corporate;

  @ApiModelProperty("经营状态")
  @NotBlank(message = "经营状态不能为空")
  private String regStatus;

  @ApiModelProperty("统一社会信用代码")
  @NotBlank(message = "统一社会信用代码不能为空")
  private String uscc;

  @ApiModelProperty("成立日期")
  @NotBlank(message = "成立日期不能为空")
  private String date;

  @ApiModelProperty("营业期限-开始")
  @NotBlank(message = "营业期限-开始不能为空")
  private String startDate;

  @ApiModelProperty("营业期限-结束")
  @NotBlank(message = "营业期限-结束不能为空")
  private String endDate;

  @ApiModelProperty("注册资本")
  @NotBlank(message = "注册资本不能为空")
  private String regCapital;

  @ApiModelProperty("公司性质")
  @NotBlank(message = "公司性质不能为空")
  private String enterpriseNature;

  @ApiModelProperty("实缴资本")
  private String paidCapital;

  @ApiModelProperty("参保人数")
  private String insNum;

  @ApiModelProperty("纳税人识别号")
  @NotBlank(message = "纳税人识别号不能为空")
  private String taxNumber;

  @ApiModelProperty("曾用名")
  private String usedName;

  @ApiModelProperty("纳税人资质")
  private String taxQualification;

  @ApiModelProperty("英文名称")
  private String englishName;

  @ApiModelProperty("登记机关")
  private String regAuthority;

  @ApiModelProperty("工商注册号")
  private String regNo;

  @ApiModelProperty("组织机构代码")
  private String orgCode;

  @ApiModelProperty("行业")
  @NotBlank(message = "行业不能为空")
  private String industry;

  @ApiModelProperty("人员规模")
  private String peopleNum;

  @ApiModelProperty("注册地址")
  @NotBlank(message = "注册地址不能为空")
  private String regAddress;

  @ApiModelProperty("经营范围")
  @NotBlank(message = "经营范围不能为空")
  private String businessScope;

  @ApiModelProperty("营业执照")
  private String licenseUrl;

  public Supplier updateBusinessInfo(Supplier supplier) {
    supplier.setEnterpriseName(enterpriseName);
    supplier.setCorporate(corporate);
    supplier.setManageType(regStatus);
    supplier.setUscc(uscc);
    supplier.setRegCapital(regCapital);
    supplier.setDate(DateUtils.parseNormalDateToTimeStamp(date));
    supplier.setStartDate(DateUtils.parseNormalDateToTimeStamp(startDate));
    supplier.setEndDate(DateUtils.parseNormalDateToTimeStamp(endDate));
    supplier.setEnterpriseNature(enterpriseNature);
    supplier.setPaidCapital(paidCapital);
    supplier.setInsNum(insNum);
    supplier.setTaxNumber(taxNumber);
    supplier.setUsedName(usedName);
    supplier.setTaxQualification(taxQualification);
    supplier.setEnglishName(englishName);
    supplier.setRegAuthority(regAuthority);
    supplier.setRegNo(regNo);
    supplier.setOrgCode(orgCode);
    supplier.setIndustry(industry);
    supplier.setPeopleNum(peopleNum);
    supplier.setRegAddress(regAddress);
    supplier.setBusinessScope(businessScope);
    supplier.setLicenseUrl(licenseUrl);
    // 修改供应商状态
    supplier.setManageId(supplier.getPurchaserId());
    supplier.setEditTime(System.currentTimeMillis());
    supplier.setIsFromSupplier(Constants.YES);
    supplier.setAuditState(Constants.AUDIT_STATE_PURCHASEIN);
    return supplier;
  }
}

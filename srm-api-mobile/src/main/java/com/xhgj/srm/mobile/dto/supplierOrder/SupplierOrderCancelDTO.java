package com.xhgj.srm.mobile.dto.supplierOrder;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> @ClassName SupplierOrderShipDTO
 */
@Data
public class SupplierOrderCancelDTO {
  @ApiModelProperty("取消单id")
  private String id;

  @ApiModelProperty("取消单编号")
  private String returnNumber;

  @ApiModelProperty("取消时间")
  private String cancelTime;

  @ApiModelProperty("状态 （0 --已取消）")
  private String state;

  @ApiModelProperty("状态 （0 --已取消）")
  private String stateToName;

  @ApiModelProperty("取消物料明细")
  private List<CancelProductDTO> cancelProductDTOList;

  public SupplierOrderCancelDTO(
      SupplierOrderToForm supplierOrderToForm, List<CancelProductDTO> cancelProductDTOList) {
    this.id = supplierOrderToForm.getId();
    this.returnNumber = StrUtil.emptyIfNull(supplierOrderToForm.getNumbers());
    this.cancelTime =
        ObjectUtil.isNotEmpty(supplierOrderToForm.getTime()) && supplierOrderToForm.getTime() > 0
            ? DateUtil.format(
                new Date(supplierOrderToForm.getTime()), DatePattern.NORM_DATETIME_PATTERN)
            : "";
    this.state = StrUtil.emptyIfNull(supplierOrderToForm.getStatus());

    SupplierOrderFormStatus valueByStatus;
    try{
      valueByStatus = SupplierOrderFormStatus.findValueByStatus(supplierOrderToForm.getStatus());
    }catch (Exception e){
      valueByStatus = null;
    }
    this.state = valueByStatus != null ? valueByStatus.getDesc() : "";
    this.cancelProductDTOList = cancelProductDTOList;
  }
}

package com.xhgj.srm.jpa.dto.purchase.order;

import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * 订单对应订单明细
 */
@Data
public class SupplierOrder2Details {
  /**
   * 对应订单
   */
  SupplierOrder supplierOrder;

  /**
   * 对应订单明细
   */
  List<SupplierOrderDetail> supplierOrderDetails;

  public SupplierOrder2Details(SupplierOrder supplierOrder, List<SupplierOrderDetail> supplierOrderDetails) {
    this.supplierOrder = supplierOrder;
    this.supplierOrderDetails = supplierOrderDetails;
  }

}

package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.dao.NoticeDao;
import com.xhgj.srm.jpa.entity.Notice;
import com.xhiot.boot.core.common.util.ObjectUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

@Repository
public class NoticeDaoImpl extends AbstractExtDao<Notice> implements NoticeDao {

  @Override
  public Page<Notice> getPage(String title, Integer rangType,Integer state, int pageNo, int pageSize) {
    String hql = "from Notice  where 1 = 1 ";
    Object[] params = new Object[]{};
    if (!StringUtils.isNullOrEmpty(title)) {
      hql += " and title like ? ";
      params = ObjectUtils.objectAdd(params, "%" + title + "%");
    }
    if (state != null) {
      hql += " and state = ? ";
      params = ObjectUtils.objectAdd(params,  state );
    }
    if (rangType != null && rangType != 3) {
      hql += " and (rangType = 3 or rangType = ? )";
      params = ObjectUtils.objectAdd(params, rangType);
    }
    hql += " order by createTime desc";
    return findPageObject(hql, params, pageNo, pageSize);
  }

  @Override
  public Page<Notice> getPageNew(String title, Integer rangType,Integer state,String codes, int pageNo,
      int pageSize) {
    String hql = "select * from t_notice  where 1 = 1 ";
    Object[] params = new Object[]{};
    if (state != null) {
      hql += " and c_state = ? ";
      params = ObjectUtils.objectAdd(params,  state );
    }

    if(StrUtil.isNotEmpty(codes)){
      hql += " and (";
      String[] split = codes.split(",");
      for (int i = 0; i < split.length; i++) {
        if(i == 0){
          hql += "FIND_IN_SET(?,c_codes) ";
          params = ObjectUtils.objectAdd(params,split[i]);
        }else {
          hql += "OR FIND_IN_SET(?,c_codes) ";
          params = ObjectUtils.objectAdd(params,split[i]);
        }
      }
      if (rangType != null && rangType == 2) {
        hql += " or (c_range_type = 3 OR (c_range_type = ? AND c_codes is NULL))";
        params = ObjectUtils.objectAdd(params, rangType);
      }
      if (rangType != null && rangType == 3) {
        hql += " or (c_range_type = 1 OR c_range_type = 3 OR (c_range_type = 2 AND c_codes is NULL))";
      }
      hql += ")";
    }else {
      if (rangType != null && rangType == 2) {
        hql += " and (c_range_type = 3 OR (c_range_type = ? AND c_codes is NULL))";
        params = ObjectUtils.objectAdd(params, rangType);
      }
      if (rangType != null && rangType == 3) {
        hql += " and (c_range_type = 1 OR c_range_type = 3 OR (c_range_type = 2 AND c_codes is NULL))";
      }
    }

    if (!StringUtils.isNullOrEmpty(title)) {
      hql += " and c_title like ? ";
      params = ObjectUtils.objectAdd(params, "%" + title + "%");
    }
    hql += " order by c_create_time desc";
    return findPageSql(hql, params, pageNo, pageSize);
  }

  @Override
  public Notice getLastNotice(Integer rangType, Integer state) {
    String hql = "select * from t_notice  where 1 = 1 ";
    Object[] params = new Object[]{};
    if (state != null) {
      hql += " and c_state = ? ";
      params = ObjectUtils.objectAdd(params,  state );
    }
    if (rangType != null && rangType != 3) {
      hql += " and (c_range_type = 3 or c_range_type = ? )";
      params = ObjectUtils.objectAdd(params, rangType);
    }
    hql += " and c_create_time >= unix_timestamp(DATE_SUB(CURDATE(), INTERVAL 30 DAY)) * 1000 order by c_create_time desc limit 1";
    return getUniqueSqlEntity(hql, params);
  }

  @Override
  public Notice getLastNoticeNew(Integer rangType, Integer state, String codes) {
    String hql = "select * from t_notice  where 1 = 1 ";
    Object[] params = new Object[]{};
    if (state != null) {
      hql += " and c_state = ? ";
      params = ObjectUtils.objectAdd(params,  state );
    }
    if(StrUtil.isNotEmpty(codes)){
      hql += " and (";
      String[] split = codes.split(",");
      for (int i = 0; i < split.length; i++) {
        if(i == 0){
          hql += "FIND_IN_SET(?,c_codes) ";
          params = ObjectUtils.objectAdd(params,split[i]);
        }else {
          hql += "OR FIND_IN_SET(?,c_codes) ";
          params = ObjectUtils.objectAdd(params,split[i]);
        }
      }
      if (rangType != null && rangType == 2) {
        hql += " or (c_range_type = 3 OR (c_range_type = ? AND c_codes is NULL))";
        params = ObjectUtils.objectAdd(params, rangType);
      }
      if (rangType != null && rangType == 3) {
        hql += " or (c_range_type = 1 OR c_range_type = 3 OR (c_range_type = 2 AND c_codes is NULL))";
      }
      hql += ")";
    }else {
      if (rangType != null && rangType == 2) {
        hql += " and (c_range_type = 3 OR (c_range_type = ? AND c_codes is NULL))";
        params = ObjectUtils.objectAdd(params, rangType);
      }
      if (rangType != null && rangType == 3) {
        hql += " and (c_range_type = 1 OR c_range_type = 3 OR (c_range_type = 2 AND c_codes is NULL))";
      }
    }
    hql += "and c_create_time >= unix_timestamp(DATE_SUB(CURDATE(), INTERVAL 30 DAY)) * 1000 order by c_create_time desc limit 1";
    return getUniqueSqlEntity(hql, params);
  }
}

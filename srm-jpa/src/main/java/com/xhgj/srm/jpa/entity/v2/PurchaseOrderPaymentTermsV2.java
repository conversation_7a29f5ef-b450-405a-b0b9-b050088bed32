package com.xhgj.srm.jpa.entity.v2;

import com.xhgj.srm.jpa.annotations.ShardingTable;
import com.xhgj.srm.jpa.entity.BasePurchaseOrderPaymentTerms;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.DynamicUpdate;
import javax.persistence.*;

@Table(name = "t_purchase_order_payment_terms")
@Data
@SuperBuilder
@NoArgsConstructor
@Entity
@DynamicUpdate
@ShardingTable(
    logicTable = "t_purchase_order_payment_terms",
    actualDataNodes = "ds0.t_purchase_order_payment_terms,ds0.t_purchase_order_payment_terms_v2"
)
@EqualsAndHashCode(callSuper = true)
public class PurchaseOrderPaymentTermsV2 extends BasePurchaseOrderPaymentTerms {

}

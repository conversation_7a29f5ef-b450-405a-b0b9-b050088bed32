package com.xhgj.srm.dto.order.invoice;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class OtherInvoiceParam {

  @ApiModelProperty(value = "发票单id")
  private String relationId;
  @ApiModelProperty(value = "购方")
  @NotBlank
  private String purchaser;
  @ApiModelProperty(value = "销方")
  @NotBlank
  private String seller;
  @ApiModelProperty("供应商id")
  @NotBlank
  private String supplierId;
  @ApiModelProperty("发票号")
  @NotBlank
  private String invoiceNum;

  public String getSeller() {
    return seller.replace("（", "(").replace("）", ")");
  }
  public String getPurchaser() {
    return purchaser.replace("（", "(").replace("）", ")");
  }
}

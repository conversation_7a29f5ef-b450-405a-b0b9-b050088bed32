package com.xhgj.srm.migration.service.impl;/**
 * @since 2025/5/30 16:17
 */
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.OrderGoodsStateV2Enum;
import com.xhgj.srm.common.enums.SimpleBooleanEnum;
import com.xhgj.srm.common.enums.VerifyConfigTypeEnum;
import com.xhgj.srm.factory.MapStructFactory;
import com.xhgj.srm.jpa.entity.PurchaseApplyForOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.VerifyConfig;
import com.xhgj.srm.jpa.entity.v2.PurchaseApplyForOrderV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.migration.MigrationRecord;
import com.xhgj.srm.jpa.repository.AsmDisOrderItemRepository;
import com.xhgj.srm.jpa.repository.MigrationRecordRepository;
import com.xhgj.srm.jpa.repository.PurchaseApplyForOrderRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderDetailRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.jpa.repository.VerifyConfigRepository;
import com.xhgj.srm.migration.service.MigrationService;
import com.xhgj.srm.v2.repository.PurchaseApplyForOrderV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderDetailV2Repository;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 *<AUTHOR>
 *@date 2025/5/30 16:17:10
 *@description
 */
@Service
@Slf4j
public class ApplyForSupplierOrderMigrationServiceImpl implements MigrationService {
  public static final String TABLE = "t_purchase_apply_for_order";
  @Resource
  private MigrationRecordRepository migrationRecordRepository;
  @Resource
  private AsmDisOrderItemRepository asmDisOrderItemRepository;
  @Resource
  private VerifyConfigRepository verifyConfigRepository;

  // ------v1--------
  @Resource
  private PurchaseApplyForOrderRepository purchaseApplyForOrderRepository;
  @Resource
  private SupplierOrderDetailRepository supplierOrderDetailRepository;
  // -----v2--------
  @Resource
  private PurchaseApplyForOrderV2Repository purchaseApplyForOrderV2Repository;
  @Resource
  private SupplierOrderDetailV2Repository supplierOrderDetailV2Repository;


  @Override
  public String migrateTable(String originId, String relationId, String batchNo,
      Map<String, Object> ext) {
    // 1.判断v1版本是否存在
    PurchaseApplyForOrder purchaseApplyForOrder = purchaseApplyForOrderRepository.findById(originId)
        .orElseThrow(() -> new CheckException("采购申请不存在"));
    // 判断此组织是否开启V2权限
    // 获取第一个的组织
    VerifyConfig verifyConfig = verifyConfigRepository.findFirstByConfigTypeAndEnable(
        VerifyConfigTypeEnum.WORKBENCH_TWO_ZERO_AVAILABLE_ORG.getCode(), Boolean.TRUE);
    List<String> organizationRoleList = verifyConfig.getOrganizationRoleList();
    if (!organizationRoleList.contains(purchaseApplyForOrder.getPurchasingOrganization())) {
      throw new CheckException("采购组织未开通2.0权限，请联系管理员");
    }
    // 2.迁移v1版本到v2版本
    PurchaseApplyForOrderV2 purchaseApplyForOrderV2 = MapStructFactory.INSTANCE.toPurchaseApplyForOrderV2(purchaseApplyForOrder);
    // 3.其他规则
    // #rule 可订货→→可订货 不可订货→→订货完成
    if (purchaseApplyForOrder.getOrderGoodsState().equals(SimpleBooleanEnum.YES.getKey())) {
      purchaseApplyForOrderV2.setOrderGoodsState(OrderGoodsStateV2Enum.CAN_ORDER.getKey());
    }
    if (purchaseApplyForOrder.getOrderGoodsState().equals(SimpleBooleanEnum.NO.getKey())) {
      purchaseApplyForOrderV2.setOrderGoodsState(OrderGoodsStateV2Enum.ORDER_COMPLETE.getKey());
    }
    // #rule 采购员拆分
    String purchaseMan = purchaseApplyForOrder.getPurchaseMan();
    // 拆分前四位
    String purchaseManNumber = purchaseMan.substring(0, 4);
    // 拆分后面
    String purchaseManName = purchaseMan.substring(4);
    purchaseApplyForOrderV2.setPurchaseMan(purchaseManName);
    purchaseApplyForOrderV2.setPurchaseManNumber("xhgj00" + purchaseManNumber);
    purchaseApplyForOrderV2.setFactoryCode(purchaseApplyForOrderV2.getPurchasingOrganization());
    purchaseApplyForOrderV2.setCancellationState(StrUtil.blankToDefault(purchaseApplyForOrder.getCancellationState(), Constants.STATE_NO));
    // 4.save
    purchaseApplyForOrderV2Repository.saveAndFlush(purchaseApplyForOrderV2);
    purchaseApplyForOrder.setState(Constants.STATE_DELETE);
    purchaseApplyForOrderRepository.saveAndFlush(purchaseApplyForOrder);
    // 5.记录日志
    this.recordMigration(originId, purchaseApplyForOrderV2.getId(), batchNo, migrationRecordRepository);
    return purchaseApplyForOrderV2.getId();
  }

  @Override
  public List<String> getOriginIds(String relationId) {
    throw new UnsupportedOperationException("不支持此操作");
  }

  @Override
  public void refreshLinkTable(String originId, String newId) {
    // 关联采购订单明细的相关关联id更新
    List<SupplierOrderDetail> supplierOrderDetailList =
        supplierOrderDetailRepository.findAllByPurchaseApplyForOrderId(originId);
    supplierOrderDetailList.forEach(item -> {
      item.setPurchaseApplyForOrderId(newId);
      supplierOrderDetailRepository.saveAndFlush(item);
    });
    List<SupplierOrderDetailV2> supplierOrderDetailV2List =
        supplierOrderDetailV2Repository.findAllByPurchaseApplyForOrderId(originId);
    supplierOrderDetailV2List.forEach(item -> {
      item.setPurchaseApplyForOrderId(newId);
      supplierOrderDetailV2Repository.saveAndFlush(item);
    });
    // 组装拆卸单相关关联id更新
    asmDisOrderItemRepository.findByPurchaseApplyForOrderIdInAndState(Collections.singletonList(originId), Constants.STATE_OK)
        .forEach(item -> {
          item.setPurchaseApplyForOrderId(newId);
          asmDisOrderItemRepository.saveAndFlush(item);
        });
  }

  @Override
  public void rollback(MigrationRecord migrationRecord) {
    // 查询v2版本
    PurchaseApplyForOrderV2 purchaseApplyForOrderV2 = purchaseApplyForOrderV2Repository.findById(migrationRecord.getNewId())
        .orElseThrow(() -> new CheckException("采购申请不存在"));
    // 删除v2版本
    purchaseApplyForOrderV2.setState(Constants.STATE_DELETE);
    purchaseApplyForOrderV2Repository.saveAndFlush(purchaseApplyForOrderV2);
    // 恢复v1版本
    PurchaseApplyForOrder purchaseApplyForOrder = purchaseApplyForOrderRepository.findById(migrationRecord.getOriginId())
        .orElseThrow(() -> new CheckException("采购申请不存在"));
    purchaseApplyForOrder.setState(Constants.STATE_OK);
    purchaseApplyForOrderRepository.saveAndFlush(purchaseApplyForOrder);
    // 恢复相关关联关系
    this.refreshLinkTableRollback(migrationRecord.getOriginId(), migrationRecord.getNewId());
    migrationRecord.setState(Constants.STATE_DELETE);
    migrationRecordRepository.saveAndFlush(migrationRecord);
  }

  @Override
  public void refreshLinkTableRollback(String originId, String newId) {
    List<SupplierOrderDetail> supplierOrderDetailList =
        supplierOrderDetailRepository.findAllByPurchaseApplyForOrderId(newId);
    supplierOrderDetailList.forEach(item -> {
      item.setPurchaseApplyForOrderId(originId);
      supplierOrderDetailRepository.saveAndFlush(item);
    });
    List<SupplierOrderDetailV2> supplierOrderDetailV2List =
        supplierOrderDetailV2Repository.findAllByPurchaseApplyForOrderId(newId);
    supplierOrderDetailV2List.forEach(item -> {
      item.setPurchaseApplyForOrderId(originId);
      supplierOrderDetailV2Repository.saveAndFlush(item);
    });
    asmDisOrderItemRepository.findByPurchaseApplyForOrderIdInAndState(Collections.singletonList(newId), Constants.STATE_OK)
        .forEach(item -> {
          item.setPurchaseApplyForOrderId(originId);
          asmDisOrderItemRepository.saveAndFlush(item);
        });
  }

  @Override
  public String getTableName() {
    return TABLE;
  }
}

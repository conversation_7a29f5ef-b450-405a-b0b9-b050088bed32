CREATE TABLE `t_platform` (
                            `id` varchar(32) NOT NULL COMMENT '主键',
                            `c_code` varchar(100) DEFAULT NULL COMMENT '平台编码',
                            `c_name` varchar(100) DEFAULT NULL COMMENT '平台名称',
                            `default_purchase` varchar(32) DEFAULT NULL COMMENT '默认采购id',
                            `c_merchant_num` int(11) DEFAULT NULL COMMENT '落地商数量',
                            `c_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                            `c_update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
                            `c_create_user` varchar(32) DEFAULT NULL COMMENT '创建人',
                            `c_update_user` varchar(32) DEFAULT NULL COMMENT '更新人',
                            `c_state` varchar(1) DEFAULT NULL COMMENT '数据状态',
                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='下单平台';

ALTER TABLE t_platform DROP COLUMN c_merchant_num;

CREATE TABLE t_platform_to_menu (
                                           id varchar(32) NOT NULL COMMENT 'id',
                                           platform_id varchar(32) NULL COMMENT '平台id',
                                           menu_id varchar(32) NULL COMMENT '菜单id',
                                           c_state varchar(1) NULL COMMENT '数据状态',
                                           CONSTRAINT t_platform_to_menu_pk PRIMARY KEY (id)
)
  ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci
COMMENT='平台 菜单 映射表';


CREATE TABLE t_supplier_to_menu (
                                           id varchar(32) NOT NULL COMMENT 'id',
                                           supplier_id varchar(32) NULL COMMENT '供应商id',
                                           menu_id varchar(32) NULL COMMENT '菜单id',
                                           c_state varchar(1) NULL COMMENT '数据状态',
                                           CONSTRAINT t_supplier_to_menu_pk PRIMARY KEY (id)
)
  ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;


CREATE TABLE `t_product_sellable_area` (
                                         `id` varchar(32) NOT NULL COMMENT '主键',
                                         `c_product_code` varchar(32) DEFAULT NULL COMMENT '物料编码',
                                         `c_sell_area` varchar(255) DEFAULT NULL COMMENT '可售区域',
                                         `c_stock` bigint(11) DEFAULT NULL COMMENT '库存',
                                         `c_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                                         `c_update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
                                         `c_create_man` varchar(32) DEFAULT NULL COMMENT '创建人',
                                         `c_update_man` varchar(32) DEFAULT NULL COMMENT '更新人',
                                         `supplier_id` varchar(32) DEFAULT NULL COMMENT '供应商id',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物料可售区域';


ALTER TABLE t_order_filing ADD c_ding_approval_man varchar(32) NULL COMMENT '最终审批人';
ALTER TABLE t_order_filing_detail MODIFY COLUMN c_num decimal(20,10) DEFAULT 0 NULL COMMENT '数量';
ALTER TABLE t_order_filing MODIFY COLUMN c_num decimal(20,10) DEFAULT 0 NULL COMMENT '报备数量';

ALTER TABLE t_financial MODIFY COLUMN c_bankAccount varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '账户名称';








